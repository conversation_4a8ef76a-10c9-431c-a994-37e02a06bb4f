-- Migration script to add merchant_ref column to merchant table
-- This enables merchants to have a reference field for external system integration
-- Date: 2025-07-22
-- Description: Add merchant_ref field to merchant table for create, update, and view operations

-- Check if the column already exists
DO $$
BEGIN
    -- Check if merchant_ref column exists
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'merchant' 
        AND column_name = 'merchant_ref'
    ) THEN
        -- Add merchant_ref column to merchant table
        ALTER TABLE merchant 
        ADD COLUMN merchant_ref VARCHAR(255);
        
        RAISE NOTICE 'Added merchant_ref column to merchant table';
    ELSE
        RAISE NOTICE 'merchant_ref column already exists in merchant table';
    END IF;
END $$;

-- Add comment to document the column purpose
COMMENT ON COLUMN merchant.merchant_ref IS 'External reference field for merchant identification and integration with external systems';

-- Create index for better performance on merchant_ref lookups (optional)
CREATE INDEX IF NOT EXISTS idx_merchant_merchant_ref ON merchant(merchant_ref);

-- Verify the changes
SELECT 
  column_name, 
  data_type, 
  character_maximum_length,
  is_nullable, 
  column_default
FROM information_schema.columns 
WHERE table_name = 'merchant' 
  AND column_name = 'merchant_ref';

-- Show sample data structure
SELECT 
    'Sample merchant table structure' as info,
    merchant_id,
    merchant_name,
    merchant_vat,
    merchant_ref,
    merchant_type,
    active,
    create_by,
    create_dt
FROM merchant 
ORDER BY merchant_id 
LIMIT 3;

-- Migration completed successfully
SELECT 'Migration completed: merchant_ref field added to merchant table' as status;
