-- Migration: Add transaction_app_id column to transaction_e_pos table
-- Date: 2025-07-22
-- Description: Adds transaction_app_id field to store WeChat appid or similar application identifiers

-- Check if the column already exists
DO $$
BEGIN
    -- Check if transaction_app_id column exists
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'transaction_e_pos' 
        AND column_name = 'transaction_app_id'
    ) THEN
        -- Add transaction_app_id column to transaction_e_pos table
        ALTER TABLE transaction_e_pos 
        ADD COLUMN transaction_app_id VARCHAR(100);
        
        RAISE NOTICE 'Added transaction_app_id column to transaction_e_pos table';
    ELSE
        RAISE NOTICE 'transaction_app_id column already exists in transaction_e_pos table';
    END IF;
END $$;

-- Add comment to document the column purpose
COMMENT ON COLUMN transaction_e_pos.transaction_app_id IS 'Application ID from payment system (e.g., WeChat appid, Alipay app_id)';

-- Create index for better performance on transaction_app_id lookups
CREATE INDEX IF NOT EXISTS idx_transaction_e_pos_app_id ON transaction_e_pos(transaction_app_id);

-- Verify the changes
SELECT 
  column_name, 
  data_type, 
  character_maximum_length,
  is_nullable, 
  column_default
FROM information_schema.columns 
WHERE table_name = 'transaction_e_pos' 
  AND column_name = 'transaction_app_id';

-- Show updated table structure
SELECT 
  column_name, 
  data_type, 
  character_maximum_length,
  is_nullable
FROM information_schema.columns 
WHERE table_name = 'transaction_e_pos' 
ORDER BY ordinal_position;
